import discord
from discord.ext import commands
from collections import defaultdict
import asyncio

from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators


async def setup(bot):
    await bot.add_cog(Vanity(bot))


class Vanity(commands.Cog):
    """
    Track guild vanity URL usage
    """

    def __init__(self, bot):
        self.bot = bot
        self.vanity_cache = defaultdict(dict)  # Cache for vanity tracking data
        bot.loop.create_task(self.load_vanity_data())

    async def load_vanity_data(self):
        """Load vanity tracking data from database"""
        if not self.bot.cxn:
            return
        try:
            query = """
                    SELECT server_id, vanity_url FROM vanity_tracker;
                    """
            records = await self.bot.cxn.fetch(query)
            for record in records:
                self.vanity_cache[record['server_id']] = {
                    'vanity_url': record['vanity_url'],
                    'total_uses': 0,
                    'current_uses': 0,
                    'enabled': True
                }
        except Exception as e:
            print(f"Failed to load vanity data: {e}")

    async def get_vanity_data(self, guild_id):
        """Get vanity data for a guild"""
        if guild_id not in self.vanity_cache:
            query = """
                    SELECT server_id, vanity_url FROM vanity_tracker
                    WHERE server_id = $1;
                    """
            record = await self.bot.cxn.fetchrow(query, guild_id)
            if record:
                self.vanity_cache[guild_id] = {
                    'vanity_url': record['vanity_url'],
                    'total_uses': 0,
                    'current_uses': 0,
                    'enabled': True
                }
            else:
                return None
        return self.vanity_cache.get(guild_id)

    async def create_vanity_data(self, guild_id, vanity_url):
        """Create vanity tracking data for a guild"""
        # Check if entry already exists
        existing = await self.bot.cxn.fetchrow(
            "SELECT server_id FROM vanity_tracker WHERE server_id = $1", guild_id
        )

        if existing:
            # Update existing entry
            query = """
                    UPDATE vanity_tracker SET vanity_url = $2 WHERE server_id = $1;
                    """
            await self.bot.cxn.execute(query, guild_id, vanity_url)
        else:
            # Insert new entry
            query = """
                    INSERT INTO vanity_tracker (server_id, vanity_url) VALUES ($1, $2);
                    """
            await self.bot.cxn.execute(query, guild_id, vanity_url)

        self.vanity_cache[guild_id] = {
            'vanity_url': vanity_url,
            'total_uses': 0,
            'current_uses': 0,
            'enabled': True
        }

    async def update_vanity_uses(self, guild_id, current_uses):
        """Update vanity usage count"""
        data = await self.get_vanity_data(guild_id)
        if not data or not data['enabled']:
            return

        # Calculate new total uses (if current uses increased)
        old_current = data['current_uses']
        new_total = data['total_uses']

        if current_uses > old_current:
            new_total += (current_uses - old_current)

        # Update cache only (no database update for total_uses since column doesn't exist)
        self.vanity_cache[guild_id]['current_uses'] = current_uses
        self.vanity_cache[guild_id]['total_uses'] = new_total

    @decorators.group(
        invoke_without_command=True,
        brief="Track guild vanity URL usage",
        implemented="2024-07-20 00:00:00.000000",
        updated="2024-07-20 00:00:00.000000",
    )
    @checks.guild_only()
    async def vanity(self, ctx):
        """
        Usage: {0}vanity
        Permission: None
        Output:
            Shows the current vanity URL usage
            statistics for this server.
        """
        if not ctx.guild.vanity_url:
            return await ctx.fail("This server doesn't have a vanity URL.")

        data = await self.get_vanity_data(ctx.guild.id)
        if not data:
            # Try to get current vanity invite and set up tracking
            try:
                vanity_invite = await ctx.guild.vanity_invite()
                if vanity_invite:
                    await self.create_vanity_data(ctx.guild.id, ctx.guild.vanity_url)
                    data = await self.get_vanity_data(ctx.guild.id)
                    # Update with current uses
                    await self.update_vanity_uses(ctx.guild.id, vanity_invite.uses or 0)
                    data = await self.get_vanity_data(ctx.guild.id)
            except discord.Forbidden:
                return await ctx.fail("I don't have permission to access vanity invite information.")

        if not data:
            return await ctx.fail("Unable to retrieve vanity tracking data.")

        embed = discord.Embed(
            title="Vanity URL Statistics",
            color=0x323339
        )

        embed.add_field(
            name="Vanity URL",
            value=f"discord.gg/{ctx.guild.vanity_url}",
            inline=False
        )
        
        embed.add_field(
            name="Current Uses",
            value=str(data['current_uses']),
            inline=True
        )
        
        embed.add_field(
            name="Total Uses",
            value=str(data['total_uses']),
            inline=True
        )
        
        embed.add_field(
            name="Status",
            value="Enabled" if data['enabled'] else "Disabled",
            inline=True
        )

        await ctx.send(embed=embed)

    @vanity.command(name="enable", brief="Enable vanity tracking for this server")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def vanity_enable(self, ctx):
        """
        Usage: {0}vanity enable
        Permission: Manage Guild
        Output:
            Enables vanity URL tracking for this server.
        """
        if not ctx.guild.vanity_url:
            return await ctx.fail("This server doesn't have a vanity URL.")

        try:
            vanity_invite = await ctx.guild.vanity_invite()
            if not vanity_invite:
                return await ctx.fail("Unable to access vanity invite information.")
        except discord.Forbidden:
            return await ctx.fail("I don't have permission to access vanity invite information.")

        data = await self.get_vanity_data(ctx.guild.id)
        if not data:
            await self.create_vanity_data(ctx.guild.id, ctx.guild.vanity_url)
            await self.update_vanity_uses(ctx.guild.id, vanity_invite.uses or 0)
        else:
            query = """
                    UPDATE vanity_tracker
                    SET vanity_url = $2
                    WHERE server_id = $1;
                    """
            await self.bot.cxn.execute(query, ctx.guild.id, ctx.guild.vanity_url)
            self.vanity_cache[ctx.guild.id]['enabled'] = True
            self.vanity_cache[ctx.guild.id]['vanity_url'] = ctx.guild.vanity_url

        await ctx.success("Vanity tracking has been enabled.")

    @vanity.command(name="disable", brief="Disable vanity tracking for this server")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def vanity_disable(self, ctx):
        """
        Usage: {0}vanity disable
        Permission: Manage Guild
        Output:
            Disables vanity URL tracking for this server.
        """
        data = await self.get_vanity_data(ctx.guild.id)
        if not data:
            return await ctx.fail("Vanity tracking is not set up for this server.")

        # Just update cache since enabled column doesn't exist in database
        self.vanity_cache[ctx.guild.id]['enabled'] = False

        await ctx.success("Vanity tracking has been disabled.")

    @vanity.command(name="reset", brief="Reset vanity tracking statistics")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def vanity_reset(self, ctx):
        """
        Usage: {0}vanity reset
        Permission: Manage Guild
        Output:
            Resets vanity URL tracking statistics
            for this server.
        """
        data = await self.get_vanity_data(ctx.guild.id)
        if not data:
            return await ctx.fail("Vanity tracking is not set up for this server.")

        if await ctx.confirm("This will reset all vanity tracking statistics. Continue?"):
            # Reset cache only since total_uses/current_uses columns don't exist in database
            self.vanity_cache[ctx.guild.id]['total_uses'] = 0
            self.vanity_cache[ctx.guild.id]['current_uses'] = 0

            await ctx.success("Vanity tracking statistics have been reset.")

    @vanity.command(name="update", brief="Manually update vanity usage count")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def vanity_update(self, ctx):
        """
        Usage: {0}vanity update
        Permission: Manage Guild
        Output:
            Manually updates the vanity usage count
            from Discord's API.
        """
        if not ctx.guild.vanity_url:
            return await ctx.fail("This server doesn't have a vanity URL.")

        data = await self.get_vanity_data(ctx.guild.id)
        if not data or not data['enabled']:
            return await ctx.fail("Vanity tracking is not enabled for this server.")

        try:
            vanity_invite = await ctx.guild.vanity_invite()
            if not vanity_invite:
                return await ctx.fail("Unable to access vanity invite information.")
            
            await self.update_vanity_uses(ctx.guild.id, vanity_invite.uses or 0)
            await ctx.success(f"Updated vanity usage count to {vanity_invite.uses or 0}.")
        except discord.Forbidden:
            await ctx.fail("I don't have permission to access vanity invite information.")

    async def vanity_update_task(self):
        """Background task to update vanity usage periodically"""
        await self.bot.wait_until_ready()
        
        while not self.bot.is_closed():
            try:
                for guild_id, data in self.vanity_cache.items():
                    if not data['enabled']:
                        continue
                    
                    guild = self.bot.get_guild(guild_id)
                    if not guild or not guild.vanity_url:
                        continue
                    
                    try:
                        vanity_invite = await guild.vanity_invite()
                        if vanity_invite:
                            await self.update_vanity_uses(guild_id, vanity_invite.uses or 0)
                    except (discord.Forbidden, discord.HTTPException):
                        continue
                
                # Wait 5 minutes before next update
                await asyncio.sleep(300)
            except Exception as e:
                print(f"Error in vanity update task: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error

    def cog_load(self):
        """Start the vanity update task when cog loads"""
        self.bot.loop.create_task(self.vanity_update_task())
