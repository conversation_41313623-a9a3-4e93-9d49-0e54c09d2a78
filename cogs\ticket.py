import discord
import asyncio
import os
import json
from datetime import datetime, timezone
from discord.ext import commands
from discord import app_commands
from io import BytesIO

from discord.ext import commands
from discord import Member, Role
from typing import Union
from utilities import decorators
from utilities import pagination


async def setup(bot):
    await bot.add_cog(Tickets(bot))


def get_ticket():
    """Decorator to check if command is run in a ticket channel"""
    async def predicate(ctx: commands.Context):  
        if not ctx.bot.cxn:
            return False
        check = await ctx.bot.cxn.fetchrow(
            "SELECT * FROM opened_tickets WHERE guild_id = $1 AND channel_id = $2", 
            ctx.guild.id, ctx.channel.id
        )
        return check is not None
    return commands.check(predicate)   


async def make_transcript(channel): 
    """Generate a transcript of the ticket channel"""
    filename = f"ticket-{channel.id}.txt"
    with open(filename, "w", encoding="utf-8") as file:
        file.write(f"Ticket Transcript for #{channel.name}\n")
        file.write(f"Generated on: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}\n")
        file.write("=" * 50 + "\n\n")
        
        async for msg in channel.history(oldest_first=True, limit=None):
            timestamp = msg.created_at.strftime('%Y-%m-%d %H:%M:%S')
            author = f"{msg.author.display_name} ({msg.author.id})"
            
            if msg.content:
                file.write(f"[{timestamp}] {author}: {msg.clean_content}\n")
            
            if msg.attachments:
                for attachment in msg.attachments:
                    file.write(f"[{timestamp}] {author} uploaded: {attachment.filename} ({attachment.url})\n")
            
            if msg.embeds:
                file.write(f"[{timestamp}] {author} sent an embed\n")
    
    return filename  


class TicketButtonModal(discord.ui.Modal, title="Create Ticket Button"):
    """Modal for creating ticket buttons"""

    identifier = discord.ui.TextInput(
        label="Button Identifier",
        placeholder="Unique identifier for this button (e.g., 'support', 'bug')",
        required=True,
        max_length=50,
        style=discord.TextStyle.short 
    )

    label = discord.ui.TextInput(
        label="Button Label",
        placeholder="Text displayed on the button",
        required=True,
        max_length=80,
        style=discord.TextStyle.short 
    )

    emoji = discord.ui.TextInput(
        label="Button Emoji",
        placeholder="Emoji for the button (optional)",
        required=False,
        max_length=10,
        style=discord.TextStyle.short 
    )

    description = discord.ui.TextInput(
        label="Button Description",
        placeholder="Description shown when creating ticket",
        required=False,
        max_length=200,
        style=discord.TextStyle.long 
    )

    async def on_submit(self, interaction: discord.Interaction):
        if not interaction.client.cxn:
            return await interaction.response.send_message("Database connection unavailable.", ephemeral=True)
            
        # Check if identifier already exists
        check = await interaction.client.cxn.fetchrow(
            'SELECT * FROM ticket_buttons WHERE guild_id = $1 AND identifier = $2', 
            interaction.guild.id, self.identifier.value.lower()
        )
        
        if check is not None: 
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> A button with identifier **{self.identifier.value}** already exists",
                color=0x323339
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)
            
        # Insert new button
        await interaction.client.cxn.execute("""
            INSERT INTO ticket_buttons (guild_id, identifier, label, emoji, description)
            VALUES ($1, $2, $3, $4, $5)
        """, interaction.guild.id, self.identifier.value.lower(), self.label.value, 
        self.emoji.value or None, self.description.value or None)
        
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Created new ticket button **{self.label.value}** with identifier `{self.identifier.value}`",
            color=0x323339
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)


class TicketPanelView(discord.ui.View):
    """Dynamic view that creates buttons based on database configuration"""
    
    def __init__(self, bot, guild_id):
        super().__init__(timeout=None)
        self.bot = bot
        self.guild_id = guild_id
        self.buttons_loaded = False

    async def load_buttons(self):
        """Load buttons from database"""
        if self.buttons_loaded or not self.bot.cxn:
            return
            
        try:
            buttons = await self.bot.cxn.fetch(
                "SELECT * FROM ticket_buttons WHERE guild_id = $1 ORDER BY created_at",
                self.guild_id
            )
            
            for button_data in buttons[:25]:  # Discord limit is 25 components
                button = discord.ui.Button(
                    label=button_data['label'],
                    emoji=button_data['emoji'],
                    style=discord.ButtonStyle.gray,
                    custom_id=f"ticket_create:{button_data['identifier']}"
                )
                button.callback = self.create_ticket_callback
                self.add_item(button)
            
            self.buttons_loaded = True
        except Exception as e:
            print(f"Error loading ticket buttons: {e}")

    async def create_ticket_callback(self, interaction: discord.Interaction):
        """Handle ticket creation button clicks"""
        if not interaction.client.cxn:
            return await interaction.response.send_message("Database connection unavailable.", ephemeral=True)
        
        # Extract identifier from custom_id
        identifier = interaction.data['custom_id'].split(':')[1]
        
        # Check if user already has a ticket
        existing_ticket = await interaction.client.cxn.fetchrow(
            "SELECT * FROM opened_tickets WHERE guild_id = $1 AND user_id = $2", 
            interaction.guild.id, interaction.user.id
        )
        
        if existing_ticket is not None:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> {interaction.user.mention}: You already have a ticket opened in <#{existing_ticket['channel_id']}>",
                color=0x323339
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)
        
        # Check if user is blacklisted
        blacklist_check = await interaction.client.cxn.fetchrow(
            "SELECT * FROM ticket_blacklist WHERE guild_id = $1 AND (user_id = $2 OR role_id = ANY($3))",
            interaction.guild.id, interaction.user.id, [role.id for role in interaction.user.roles]
        )
        
        if blacklist_check:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> You are not allowed to create tickets",
                color=0x323339
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)
        
        # Get button configuration
        button_config = await interaction.client.cxn.fetchrow(
            "SELECT * FROM ticket_buttons WHERE guild_id = $1 AND identifier = $2",
            interaction.guild.id, identifier
        )
        
        if not button_config:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> Button configuration not found",
                color=0x323339
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)
        
        # Create the ticket
        await self._create_ticket(interaction, button_config)

    async def _create_ticket(self, interaction, button_config):
        """Create the actual ticket channel"""
        try:
            # Get ticket configuration
            ticket_config = await interaction.client.cxn.fetchrow(
                "SELECT * FROM ticket_config WHERE guild_id = $1", interaction.guild.id
            )
            
            # Get channel name format
            name_format = ticket_config['channel_name'] if ticket_config and ticket_config['channel_name'] else "ticket-{user}"
            channel_name = name_format.format(
                user=interaction.user.display_name.lower().replace(' ', '-'),
                identifier=button_config['identifier'],
                id=interaction.user.id
            )
            
            # Get category
            category_id = button_config['category_id'] or (ticket_config['default_category'] if ticket_config else None)
            category = interaction.client.get_channel(category_id) if category_id else None
            
            # Create channel
            ticket_channel = await interaction.guild.create_text_channel(
                name=channel_name[:100],  # Discord limit
                category=category,
                reason=f"Ticket created by {interaction.user}"
            )
            
            # Set permissions
            overwrites = {
                interaction.guild.default_role: discord.PermissionOverwrite(view_channel=False),
                interaction.user: discord.PermissionOverwrite(
                    view_channel=True,
                    send_messages=True,
                    attach_files=True,
                    embed_links=True,
                    read_message_history=True
                )
            }
            
            # Add staff roles
            staff_roles = await interaction.client.cxn.fetch(
                "SELECT role_id FROM ticket_staff WHERE guild_id = $1", interaction.guild.id
            )
            
            for staff_role_data in staff_roles:
                role = interaction.guild.get_role(staff_role_data['role_id'])
                if role:
                    overwrites[role] = discord.PermissionOverwrite(
                        view_channel=True,
                        send_messages=True,
                        attach_files=True,
                        embed_links=True,
                        read_message_history=True,
                        manage_messages=True
                    )
            
            await ticket_channel.edit(overwrites=overwrites)
            
            # Create ticket embed
            embed = discord.Embed(
                title=f"🎫 {button_config['label']} Ticket",
                description=button_config['description'] or "Support will be with you shortly. Please describe your issue in detail.",
                color=0x323339
            )
            embed.add_field(
                name="Ticket Creator", 
                value=interaction.user.mention, 
                inline=True
            )
            embed.add_field(
                name="Ticket Type", 
                value=button_config['label'], 
                inline=True
            )
            embed.set_footer(text=f"Ticket ID: {ticket_channel.id}")
            
            # Get opening message
            opening_msg = await interaction.client.cxn.fetchrow(
                "SELECT script FROM ticket_opening WHERE guild_id = $1 AND identifier = $2",
                interaction.guild.id, button_config['identifier']
            )
            
            content = opening_msg['script'].format(
                user=interaction.user.mention,
                username=interaction.user.display_name,
                server=interaction.guild.name
            ) if opening_msg else f"{interaction.user.mention}, welcome to your ticket!"
            
            # Create close button
            close_view = TicketCloseView()
            
            # Send ticket message
            message = await ticket_channel.send(
                content=content,
                embed=embed,
                view=close_view
            )
            await message.pin()
            
            # Save to database
            await interaction.client.cxn.execute("""
                INSERT INTO opened_tickets (guild_id, channel_id, user_id, button_identifier, created_at)
                VALUES ($1, $2, $3, $4, $5)
            """, interaction.guild.id, ticket_channel.id, interaction.user.id, 
            button_config['identifier'], datetime.now(timezone.utc))
            
            # Notify staff roles
            for staff_role_data in staff_roles:
                role = interaction.guild.get_role(staff_role_data['role_id'])
                if role:
                    try:
                        await ticket_channel.send(
                            f"{role.mention} New ticket created!",
                            allowed_mentions=discord.AllowedMentions(roles=True),
                            delete_after=5
                        )
                    except:
                        pass
            
            # Response
            embed = discord.Embed(
                description=f"<:pass:1395855305270755399> Created your ticket in {ticket_channel.mention}",
                color=0x323339
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            
        except Exception as e:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> Failed to create ticket: {str(e)}",
                color=0x323339
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)


class TicketCloseView(discord.ui.View):
    """View for closing tickets"""
    
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Close Ticket", emoji="🔒", style=discord.ButtonStyle.red, custom_id="ticket_close")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Confirmation view
        confirm_view = TicketCloseConfirmView()
        embed = discord.Embed(
            description="Are you sure you want to close this ticket?",
            color=0x323339
        )
        await interaction.response.send_message(embed=embed, view=confirm_view, ephemeral=True)


class TicketCloseConfirmView(discord.ui.View):
    """Confirmation view for closing tickets"""
    
    def __init__(self):
        super().__init__(timeout=60.0)

    @discord.ui.button(label="Confirm Close", style=discord.ButtonStyle.red)
    async def confirm_close(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self._close_ticket(interaction, "Closed by user")

    @discord.ui.button(label="Cancel", style=discord.ButtonStyle.gray)
    async def cancel_close(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            description="Ticket closure cancelled",
            color=0x323339
        )
        await interaction.response.edit_message(embed=embed, view=None)

    async def _close_ticket(self, interaction, reason="No reason provided"):
        """Close the ticket and handle cleanup"""
        if not interaction.client.cxn:
            return await interaction.response.send_message("Database connection unavailable.", ephemeral=True)
        
        try:
            # Get ticket info
            ticket_info = await interaction.client.cxn.fetchrow(
                "SELECT * FROM opened_tickets WHERE guild_id = $1 AND channel_id = $2",
                interaction.guild.id, interaction.channel.id
            )
            
            if not ticket_info:
                embed = discord.Embed(
                    description="<:fail:1395855303035715647> This is not a valid ticket channel",
                    color=0x323339
                )
                return await interaction.response.edit_message(embed=embed, view=None)
            
            # Get logs channel
            logs_config = await interaction.client.cxn.fetchrow(
                "SELECT logs_channel FROM ticket_config WHERE guild_id = $1", interaction.guild.id
            )
            
            # Generate transcript
            transcript_sent = False
            if logs_config and logs_config['logs_channel']:
                try:
                    filename = await make_transcript(interaction.channel)
                    
                    # Create log embed
                    log_embed = discord.Embed(
                        title="🎫 Ticket Closed",
                        color=0x323339,
                        timestamp=datetime.now(timezone.utc)
                    )
                    log_embed.add_field(name="Ticket Creator", value=f"<@{ticket_info['user_id']}>", inline=True)
                    log_embed.add_field(name="Closed By", value=interaction.user.mention, inline=True)
                    log_embed.add_field(name="Reason", value=reason, inline=True)
                    log_embed.add_field(name="Channel", value=f"#{interaction.channel.name}", inline=True)
                    log_embed.add_field(name="Created", value=discord.utils.format_dt(ticket_info['created_at'], 'R'), inline=True)
                    log_embed.set_footer(text=f"Ticket ID: {interaction.channel.id}")
                    
                    logs_channel = interaction.client.get_channel(logs_config['logs_channel'])
                    if logs_channel:
                        await logs_channel.send(embed=log_embed, file=discord.File(filename))
                        transcript_sent = True
                    
                    os.remove(filename)
                except Exception as e:
                    print(f"Error sending transcript: {e}")
            
            # Remove from database
            await interaction.client.cxn.execute(
                "DELETE FROM opened_tickets WHERE guild_id = $1 AND channel_id = $2",
                interaction.guild.id, interaction.channel.id
            )
            
            # Final message
            embed = discord.Embed(
                description=f"🔒 Ticket closed by {interaction.user.mention}\n**Reason:** {reason}",
                color=0x323339
            )
            if transcript_sent:
                embed.add_field(name="Transcript", value="✅ Sent to logs channel", inline=False)
            
            await interaction.response.edit_message(embed=embed, view=None)
            
            # Delete channel after delay
            await asyncio.sleep(5)
            await interaction.channel.delete(reason="Ticket closed")
            
        except Exception as e:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> Error closing ticket: {str(e)}",
                color=0x323339
            )
            await interaction.response.edit_message(embed=embed, view=None)


class Tickets(commands.Cog):
    """
    Comprehensive ticket system for Discord servers.
    """

    def __init__(self, bot):
        self.bot = bot
        # Initialize database tables
        self.bot.loop.create_task(self.init_db())

    async def init_db(self):
        """Initialize database tables for tickets"""
        await self.bot.wait_until_ready()
        if not self.bot.cxn:
            return

        try:
            # Main ticket configuration
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS ticket_config (
                    guild_id BIGINT PRIMARY KEY,
                    panel_message TEXT,
                    panel_channel BIGINT,
                    default_category BIGINT,
                    logs_channel BIGINT,
                    channel_name TEXT DEFAULT 'ticket-{user}'
                );
            """)

            # Ticket buttons
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS ticket_buttons (
                    guild_id BIGINT,
                    identifier TEXT,
                    label TEXT NOT NULL,
                    emoji TEXT,
                    description TEXT,
                    category_id BIGINT,
                    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
                    PRIMARY KEY (guild_id, identifier)
                );
            """)

            # Opening messages for buttons
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS ticket_opening (
                    guild_id BIGINT,
                    identifier TEXT,
                    script TEXT NOT NULL,
                    PRIMARY KEY (guild_id, identifier)
                );
            """)

            # Staff roles
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS ticket_staff (
                    guild_id BIGINT,
                    role_id BIGINT,
                    PRIMARY KEY (guild_id, role_id)
                );
            """)

            # Blacklisted users/roles
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS ticket_blacklist (
                    guild_id BIGINT,
                    user_id BIGINT,
                    role_id BIGINT,
                    reason TEXT,
                    PRIMARY KEY (guild_id, user_id, role_id)
                );
            """)

            # Opened tickets
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS opened_tickets (
                    guild_id BIGINT,
                    channel_id BIGINT,
                    user_id BIGINT,
                    button_identifier TEXT,
                    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
                    PRIMARY KEY (guild_id, channel_id)
                );
            """)

        except Exception as e:
            print(f"Error initializing ticket database: {e}")

    @commands.Cog.listener()
    async def on_guild_channel_delete(self, channel: discord.abc.GuildChannel):
        """Clean up database when ticket channels are deleted"""
        if isinstance(channel, discord.TextChannel) and self.bot.cxn:
            try:
                await self.bot.cxn.execute(
                    "DELETE FROM opened_tickets WHERE guild_id = $1 AND channel_id = $2",
                    channel.guild.id, channel.id
                )
            except Exception as e:
                print(f"Error cleaning up deleted ticket channel: {e}")

    @decorators.group(invoke_without_command=True)
    async def ticket(self, ctx):
        """
        Usage: {0}ticket
        Output: Manage support tickets
        """
        await ctx.send_help(ctx.command)

    @ticket.command(name="panel", aliases=["message", "link"])
    @commands.has_permissions(administrator=True)
    async def ticket_panel(self, ctx, *, message: str = None):
        """
        Usage: {0}ticket panel [message]
        Aliases: {0}ticket message, {0}ticket link
        Output: Set the ticket panel message
        Notes: Use variables: {user}, {server}, {mention}
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        if message is None:
            # Show current message
            config = await self.bot.cxn.fetchrow(
                "SELECT panel_message FROM ticket_config WHERE guild_id = $1", ctx.guild.id
            )
            
            current_msg = config['panel_message'] if config else "**🎫 Support Tickets**\nClick a button below to create a ticket!"
            
            embed = discord.Embed(
                title="Current Panel Message",
                description=f"```{current_msg}```",
                color=0x323339
            )
            return await ctx.send_or_reply(embed=embed)

        # Set message
        await self.bot.cxn.execute("""
            INSERT INTO ticket_config (guild_id, panel_message) VALUES ($1, $2)
            ON CONFLICT (guild_id) DO UPDATE SET panel_message = $2
        """, ctx.guild.id, message)

        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Set ticket panel message to:\n```{message[:1000]}```",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="button", aliases=["option"])
    @commands.has_permissions(administrator=True)
    async def ticket_button(self, ctx):
        """
        Usage: {0}ticket button
        Aliases: {0}ticket option
        Output: Control the buttons on the panel
        Notes: Interactive interface to manage ticket buttons
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        # Get existing buttons
        buttons = await self.bot.cxn.fetch(
            "SELECT * FROM ticket_buttons WHERE guild_id = $1 ORDER BY created_at", ctx.guild.id
        )

        embed = discord.Embed(
            title="🎫 Ticket Button Management",
            description="Choose an action below:",
            color=0x323339
        )

        if buttons:
            button_list = []
            for i, btn in enumerate(buttons, 1):
                emoji_text = f"{btn['emoji']} " if btn['emoji'] else ""
                button_list.append(f"`{i}.` {emoji_text}**{btn['label']}** (`{btn['identifier']}`)")
            
            embed.add_field(
                name="Current Buttons",
                value="\n".join(button_list[:10]),  # Limit display
                inline=False
            )

        # Create management buttons
        create_btn = discord.ui.Button(label="Create Button", style=discord.ButtonStyle.green, emoji="➕")
        delete_btn = discord.ui.Button(label="Delete Button", style=discord.ButtonStyle.red, emoji="🗑️", disabled=len(buttons) == 0)
        list_btn = discord.ui.Button(label="List Buttons", style=discord.ButtonStyle.gray, emoji="📋", disabled=len(buttons) == 0)

        async def create_callback(interaction: discord.Interaction):
            if interaction.user != ctx.author:
                embed = discord.Embed(
                    description="<:fail:1395855303035715647> You are not the author of this command.",
                    color=0x323339
                )
                return await interaction.response.send_message(embed=embed, ephemeral=True)
            
            modal = TicketButtonModal()
            await interaction.response.send_modal(modal)

        async def delete_callback(interaction: discord.Interaction):
            if interaction.user != ctx.author:
                embed = discord.Embed(
                    description="<:fail:1395855303035715647> You are not the author of this command.",
                    color=0x323339
                )
                return await interaction.response.send_message(embed=embed, ephemeral=True)
            
            options = []
            for btn in buttons:
                emoji_text = f"{btn['emoji']} " if btn['emoji'] else ""
                options.append(discord.SelectOption(
                    label=btn['label'][:100],
                    value=btn['identifier'],
                    description=f"ID: {btn['identifier']}",
                    emoji=btn['emoji']
                ))
            
            if not options:
                embed = discord.Embed(
                    description="<:fail:1395855303035715647> No buttons to delete.",
                    color=0x323339
                )
                return await interaction.response.send_message(embed=embed, ephemeral=True)

            select = discord.ui.Select(placeholder="Select a button to delete...", options=options)

            async def select_callback(select_interaction):
                if select_interaction.user != ctx.author:
                    embed = discord.Embed(
                        description="<:fail:1395855303035715647> You are not the author of this command.",
                        color=0x323339
                    )
                    return await select_interaction.response.send_message(embed=embed, ephemeral=True)
                
                identifier = select.values[0]
                await self.bot.cxn.execute(
                    "DELETE FROM ticket_buttons WHERE guild_id = $1 AND identifier = $2",
                    ctx.guild.id, identifier
                )
                
                embed = discord.Embed(
                    description=f"<:pass:1395855305270755399> Deleted button with identifier `{identifier}`",
                    color=0x323339
                )
                await select_interaction.response.edit_message(embed=embed, view=None)

            select.callback = select_callback
            view = discord.ui.View()
            view.add_item(select)
            
            embed = discord.Embed(
                description="Select a button to delete:",
                color=0x323339
            )
            await interaction.response.edit_message(embed=embed, view=view)

        async def list_callback(interaction: discord.Interaction):
            if interaction.user != ctx.author:
                embed = discord.Embed(
                    description="<:fail:1395855303035715647> You are not the author of this command.",
                    color=0x323339
                )
                return await interaction.response.send_message(embed=embed, ephemeral=True)
            
            embed = discord.Embed(
                title="🎫 Ticket Buttons",
                color=0x323339
            )
            
            for i, btn in enumerate(buttons, 1):
                emoji_text = f"{btn['emoji']} " if btn['emoji'] else ""
                embed.add_field(
                    name=f"{i}. {emoji_text}{btn['label']}",
                    value=f"**ID:** `{btn['identifier']}`\n**Description:** {btn['description'] or 'None'}",
                    inline=False
                )
            
            await interaction.response.edit_message(embed=embed, view=None)

        create_btn.callback = create_callback
        delete_btn.callback = delete_callback
        list_btn.callback = list_callback

        view = discord.ui.View()
        view.add_item(create_btn)
        view.add_item(delete_btn)
        view.add_item(list_btn)

        await ctx.send_or_reply(embed=embed, view=view)

    @ticket.command(name="open", aliases=["welcome", "opening"])
    @commands.has_permissions(administrator=True)
    async def ticket_open(self, ctx, identifier: str, *, script: str):
        """
        Usage: {0}ticket open <identifier> <script>
        Aliases: {0}ticket welcome, {0}ticket opening
        Output: Set the opening message for a ticket
        Notes: Use variables: {user}, {username}, {server}
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        # Check if button exists
        button_check = await self.bot.cxn.fetchrow(
            "SELECT * FROM ticket_buttons WHERE guild_id = $1 AND identifier = $2",
            ctx.guild.id, identifier.lower()
        )

        if not button_check:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> No button found with identifier `{identifier}`",
                color=0x323339
            )
            return await ctx.send_or_reply(embed=embed)

        # Set opening message
        await self.bot.cxn.execute("""
            INSERT INTO ticket_opening (guild_id, identifier, script) VALUES ($1, $2, $3)
            ON CONFLICT (guild_id, identifier) DO UPDATE SET script = $3
        """, ctx.guild.id, identifier.lower(), script)

        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Set opening message for `{identifier}` button:\n```{script[:1000]}```",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    # --- Add more ticket commands below ---
    @ticket.command(name="category", aliases=["redirect"])
    @commands.has_permissions(administrator=True)
    async def ticket_category(self, ctx, identifier: str, channel: discord.CategoryChannel):
        """
        Usage: {0}ticket category <identifier> <channel>
        Aliases: {0}ticket redirect
        Output: Set the category for a ticket button
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        button_check = await self.bot.cxn.fetchrow(
            "SELECT * FROM ticket_buttons WHERE guild_id = $1 AND identifier = $2",
            ctx.guild.id, identifier.lower()
        )
        if not button_check:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> No button found with identifier `{identifier}`",
                color=0x323339
            )
            return await ctx.send_or_reply(embed=embed)

        await self.bot.cxn.execute(
            "UPDATE ticket_buttons SET category_id = $1 WHERE guild_id = $2 AND identifier = $3",
            channel.id, ctx.guild.id, identifier.lower()
        )
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Set category for `{identifier}` to {channel.mention}",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="name", aliases=["channel"])
    @commands.has_permissions(administrator=True)
    async def ticket_name(self, ctx, *, name: str):
        """
        Usage: {0}ticket name <name>
        Aliases: {0}ticket channel
        Output: Set the name for new ticket channels
        Notes: Use variables: {user}, {identifier}, {id}
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        await self.bot.cxn.execute(
            "INSERT INTO ticket_config (guild_id, channel_name) VALUES ($1, $2) ON CONFLICT (guild_id) DO UPDATE SET channel_name = $2",
            ctx.guild.id, name
        )
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Set ticket channel name format to:\n```{name[:100]}```",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="staff")
    @commands.has_permissions(administrator=True)
    async def ticket_staff(self, ctx, role: discord.Role):
        """
        Usage: {0}ticket staff <role>
        Output: Allow a role to see new tickets
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        await self.bot.cxn.execute(
            "INSERT INTO ticket_staff (guild_id, role_id) VALUES ($1, $2) ON CONFLICT DO NOTHING",
            ctx.guild.id, role.id
        )
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Added {role.mention} as ticket staff.",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="ignore", aliases=["blacklist"])
    @commands.has_permissions(administrator=True)
    async def ticket_ignore(self, ctx, target: Union[Member, Role]):
        """
        Usage: {0}ticket ignore <target>
        Aliases: {0}ticket blacklist
        Output: Prevent a role or member from creating tickets
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        user_id = target.id if isinstance(target, discord.Member) else None
        role_id = target.id if isinstance(target, discord.Role) else None

        await self.bot.cxn.execute(
            "INSERT INTO ticket_blacklist (guild_id, user_id, role_id, reason) VALUES ($1, $2, $3, $4) ON CONFLICT DO NOTHING",
            ctx.guild.id, user_id, role_id, "Blacklisted"
        )
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Blacklisted {target.mention} from creating tickets.",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="add", aliases=["allow"])
    @commands.has_permissions(manage_channels=True)
    @get_ticket()
    async def ticket_add(self, ctx, target: Union[Member, Role]):
        """
        Usage: {0}ticket add <target>
        Aliases: {0}ticket allow
        Output: Add a role or member to the ticket
        """
        overwrite = discord.PermissionOverwrite(
            view_channel=True,
            send_messages=True,
            attach_files=True,
            embed_links=True,
            read_message_history=True
        )
        await ctx.channel.set_permissions(target, overwrite=overwrite)
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Added {target.mention} to the ticket.",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="deny", aliases=["block"])
    @commands.has_permissions(manage_channels=True)
    @get_ticket()
    async def ticket_deny(self, ctx, target: Union[Member, Role]):
        """
        Usage: {0}ticket deny <target>
        Aliases: {0}ticket block
        Output: Remove a role or member's access to the ticket
        """
        await ctx.channel.set_permissions(target, view_channel=False)
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Denied {target.mention} access to the ticket.",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="remove", aliases=["hide"])
    @commands.has_permissions(manage_channels=True)
    @get_ticket()
    async def ticket_remove(self, ctx, target: Union[Member, Role]):
        """
        Usage: {0}ticket remove <target>
        Aliases: {0}ticket hide
        Output: Remove a role or member from the ticket
        """
        await ctx.channel.set_permissions(target, overwrite=None)
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Removed {target.mention} from the ticket.",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="close", aliases=["end"])
    @get_ticket()
    async def ticket_close(self, ctx, *, reason: str = "No reason provided"):
        """
        Usage: {0}ticket close [reason]
        Aliases: {0}ticket end
        Output: Close an open ticket and forward the transcript
        """
        # Use the close logic from TicketCloseConfirmView._close_ticket
        view = TicketCloseConfirmView()
        await view._close_ticket(ctx, reason)

    @ticket.command(name="setup")
    @commands.has_permissions(administrator=True)
    async def ticket_setup(self, ctx):
        """
        Usage: {0}ticket setup
        Output: Automatically create a panel, ticket message, and category channel for your tickets
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        # Create category
        category = await ctx.guild.create_category("Tickets")
        # Create panel channel
        panel_channel = await ctx.guild.create_text_channel("ticket-panel", category=category)
        # Set config
        await self.bot.cxn.execute(
            "INSERT INTO ticket_config (guild_id, panel_channel, default_category) VALUES ($1, $2, $3) ON CONFLICT (guild_id) DO UPDATE SET panel_channel = $2, default_category = $3",
            ctx.guild.id, panel_channel.id, category.id
        )
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Setup complete! Panel channel: {panel_channel.mention}, Category: {category.mention}",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="logs")
    @commands.has_permissions(administrator=True)
    async def ticket_logs(self, ctx, channel: discord.TextChannel):
        """
        Usage: {0}ticket logs <channel>
        Output: Set the channel in which ticket transcripts will be sent upon closure
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        await self.bot.cxn.execute(
            "INSERT INTO ticket_config (guild_id, logs_channel) VALUES ($1, $2) ON CONFLICT (guild_id) DO UPDATE SET logs_channel = $2",
            ctx.guild.id, channel.id
        )
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> Set logs channel to {channel.mention}",
            color=0x323339
        )
        await ctx.send_or_reply(embed=embed)

    @ticket.command(name="transcript")
    @get_ticket()
    async def ticket_transcript(self, ctx):
        """
        Usage: {0}ticket transcript
        Output: Generate and send a transcript of the ticket channel
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        try:
            filename = await make_transcript(ctx.channel)
            
            embed = discord.Embed(
                description="Transcript generated successfully.",
                color=0x323339
            )
            await ctx.send(embed=embed, file=discord.File(filename))
            
            os.remove(filename)
        except Exception as e:
            embed = discord.Embed(
                description=f"<:fail:1395855303035715647> Error generating transcript: {str(e)}",
                color=0x323339
            )
            await ctx.send(embed=embed)

    @ticket.command(name="info", aliases=["details"])
    @get_ticket()
    async def ticket_info(self, ctx):
        """
        Usage: {0}ticket info
        Aliases: {0}ticket details
        Output: Show information about the current ticket
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        ticket_info = await self.bot.cxn.fetchrow(
            "SELECT * FROM opened_tickets WHERE guild_id = $1 AND channel_id = $2",
            ctx.guild.id, ctx.channel.id
        )
        
        if not ticket_info:
            embed = discord.Embed(
                description="<:fail:1395855303035715647> This is not a valid ticket channel",
                color=0x323339
            )
            return await ctx.send(embed=embed)

        button_config = await self.bot.cxn.fetchrow(
            "SELECT * FROM ticket_buttons WHERE guild_id = $1 AND identifier = $2",
            ctx.guild.id, ticket_info['button_identifier']
        )
        
        embed = discord.Embed(
            title=f"🎫 Ticket Information",
            color=0x323339
        )
        embed.add_field(name="Ticket Creator", value=f"<@{ticket_info['user_id']}>", inline=True)
        embed.add_field(name="Ticket Channel", value=f"#{ctx.channel.name}", inline=True)
        embed.add_field(name="Ticket Type", value=button_config['label'], inline=True)
        embed.add_field(name="Created At", value=discord.utils.format_dt(ticket_info['created_at'], 'F'), inline=True)
        embed.set_footer(text=f"Ticket ID: {ctx.channel.id}")
        
        await ctx.send(embed=embed)

    @ticket.command(name="debug")
    @commands.is_owner()
    async def ticket_debug(self, ctx):
        """
        Usage: {0}ticket debug
        Output: Show debug information for the ticket system
        """
        if not self.bot.cxn:
            return await ctx.fail("Database connection unavailable.")

        # Gather debug information
        guild_config = await self.bot.cxn.fetchrow(
            "SELECT * FROM ticket_config WHERE guild_id = $1",
            ctx.guild.id
        )
        
        buttons = await self.bot.cxn.fetch(
            "SELECT * FROM ticket_buttons WHERE guild_id = $1",
            ctx.guild.id
        )
        
        opened_tickets = await self.bot.cxn.fetch(
            "SELECT * FROM opened_tickets WHERE guild_id = $1",
            ctx.guild.id
        )
        
        # Create debug embed
        embed = discord.Embed(
            title="🎫 Ticket System Debug Info",
            color=0x323339
        )
        embed.add_field(name="Guild Config", value=f"```{json.dumps(guild_config, default=str, indent=2)}```", inline=False)
        embed.add_field(name="Buttons", value=f"```{json.dumps(buttons, default=str, indent=2)}```", inline=False)
        embed.add_field(name="Opened Tickets", value=f"```{json.dumps(opened_tickets, default=str, indent=2)}```", inline=False)
        
        await ctx.send(embed=embed)