import discord
from discord.ext import commands
from datetime import datetime
import io
import json
import random
import asyncio
import logging
import textwrap
from PIL import Image, ImageDraw, ImageFont

logger = logging.getLogger(__name__)

async def setup(bot):
    await bot.add_cog(Journal(bot))

class JournalError(Exception):
    """Custom exception for journal operations."""
    pass

class JournalEntryModal(discord.ui.Modal, title="New Journal Entry"):
    """Modal for writing journal entries."""
    entry_content = discord.ui.TextInput(
        label="Your Thoughts",
        style=discord.TextStyle.paragraph,
        placeholder="Write your feelings, experiences, or thoughts here...",
        max_length=2000,
        required=True
    )
    
    mood = discord.ui.TextInput(
        label="Mood (Optional)",
        placeholder="e.g., Happy, Reflective, Tired",
        max_length=50,
        required=False
    )

    def __init__(self, cog, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cog = cog

    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Save the entry
            await self.cog.bot.cxn.execute(
                "INSERT INTO journal_entries (user_id, entry, mood) VALUES ($1, $2, $3)",
                interaction.user.id, 
                self.entry_content.value.strip(),
                self.mood.value.strip() if self.mood.value else None
            )
            
            # Get the entry count for confirmation
            count = await self.cog.bot.cxn.fetchval(
                "SELECT COUNT(*) FROM journal_entries WHERE user_id = $1",
                interaction.user.id
            )
            
            embed = discord.Embed(
                title="✏️ Entry Added",
                description=f"Your journal now has **{count}** entries.",
                color=0x00ff00
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            
        except Exception as e:
            logger.error(f"Failed to add journal entry: {e}")
            await interaction.response.send_message("❌ Failed to save entry. Please try again.", ephemeral=True)

class JournalBrowser(discord.ui.View):
    """Interactive journal browser with image rendering."""
    def __init__(self, cog, entries, starting_index, user_id):
        super().__init__(timeout=180)
        self.cog = cog
        self.entries = entries
        self.index = starting_index
        self.user_id = user_id
        
    async def interaction_check(self, interaction):
        return interaction.user.id == self.user_id

    async def render_page(self, entry):
        """Render journal entry as an image."""
        try:
            # Load journal background
            background = Image.open("assets/image.png").convert("RGBA")
            draw = ImageDraw.Draw(background)
            
            # Load fonts
            title_font = ImageFont.truetype("assets/font.ttf", 42)
            content_font = ImageFont.truetype("assets/font.ttf", 32)
            meta_font = ImageFont.truetype("assets/font.ttf", 28)
            
            # Draw title
            draw.text((80, 100), "Personal Journal", fill=(50, 50, 50), font=title_font)
            
            # Draw mood if available
            mood_text = f"Mood: {entry['mood']}" if entry['mood'] else ""
            if mood_text:
                draw.text((600, 120), mood_text, fill=(100, 100, 100), font=meta_font)
            
            # Draw date
            date_str = entry['created_at'].strftime("%B %d, %Y")
            draw.text((600, 160), date_str, fill=(100, 100, 100), font=meta_font)
            
            # Draw entry content with wrapping
            text = entry['entry']
            y_position = 240
            line_height = 40
            max_width = 700
            
            for paragraph in text.split('\n'):
                lines = textwrap.wrap(paragraph, width=60)
                for line in lines:
                    draw.text((80, y_position), line, fill=(30, 30, 30), font=content_font)
                    y_position += line_height
                y_position += line_height  # Extra space between paragraphs
            
            # Draw entry number
            draw.text((700, 980), f"Entry #{entry['id']}", fill=(100, 100, 100), font=meta_font)
            
            # Save to buffer
            img_buffer = io.BytesIO()
            background.save(img_buffer, format="PNG")
            img_buffer.seek(0)
            return img_buffer
            
        except Exception as e:
            logger.error(f"Failed to render journal image: {e}")
            return None

    async def update_message(self, interaction):
        """Update message with current journal page."""
        entry = self.entries[self.index]
        img_buffer = await self.render_page(entry)
        
        if not img_buffer:
            return await interaction.response.send_message("❌ Failed to render journal page.", ephemeral=True)
        
        file = discord.File(img_buffer, filename="journal.png")
        embed = discord.Embed(
            title=f"Journal Entry #{entry['id']}",
            description=f"Page {self.index+1} of {len(self.entries)}",
            color=0x9b59b6
        )
        embed.set_image(url="attachment://journal.png")
        embed.set_footer(text=f"Created on {entry['created_at'].strftime('%b %d, %Y at %H:%M')}")
        
        await interaction.response.edit_message(embed=embed, attachments=[file], view=self)

    @discord.ui.button(label="◀ Previous", style=discord.ButtonStyle.primary, row=0)
    async def previous_button(self, interaction, button):
        if self.index > 0:
            self.index -= 1
            await self.update_message(interaction)

    @discord.ui.button(label="Next ▶", style=discord.ButtonStyle.primary, row=0)
    async def next_button(self, interaction, button):
        if self.index < len(self.entries) - 1:
            self.index += 1
            await self.update_message(interaction)

    @discord.ui.button(label="Delete Entry", style=discord.ButtonStyle.danger, row=1)
    async def delete_button(self, interaction, button):
        entry = self.entries[self.index]
        await interaction.response.send_modal(DeleteConfirmationModal(self.cog, entry['id']))

class DeleteConfirmationModal(discord.ui.Modal, title="Confirm Deletion"):
    def __init__(self, cog, entry_id):
        super().__init__()
        self.cog = cog
        self.entry_id = entry_id
        self.confirmation = discord.ui.TextInput(
            label=f"Type 'DELETE' to confirm",
            placeholder="This action cannot be undone",
            max_length=10,
            required=True
        )
        self.add_item(self.confirmation)

    async def on_submit(self, interaction: discord.Interaction):
        if self.confirmation.value.strip().upper() != "DELETE":
            return await interaction.response.send_message("❌ Deletion cancelled. Confirmation text did not match.", ephemeral=True)
        
        try:
            await self.cog.bot.cxn.execute(
                "DELETE FROM journal_entries WHERE id = $1 AND user_id = $2",
                self.entry_id, interaction.user.id
            )
            embed = discord.Embed(
                title="🗑️ Entry Deleted",
                description=f"Entry #{self.entry_id} has been permanently removed.",
                color=0x00ff00
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
        except Exception as e:
            logger.error(f"Failed to delete entry: {e}")
            await interaction.response.send_message("❌ Failed to delete entry.", ephemeral=True)

class Journal(commands.Cog):
    """A private memory journal system with rich visual interface."""
    def __init__(self, bot):
        self.bot = bot
        self._db_ready = False

    async def ensure_table(self):
        """Ensure the journal table exists with proper schema."""
        try:
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS journal_entries (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    entry TEXT NOT NULL CHECK (length(entry) > 0),
                    mood TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            await self.bot.cxn.execute("""
                CREATE INDEX IF NOT EXISTS idx_journal_user_id 
                ON journal_entries(user_id)
            """)
            self._db_ready = True
            logger.info("Journal table initialized")
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise JournalError("Database setup failed")

    async def cog_check(self, ctx):
        if not self._db_ready:
            await self.ensure_table()
        return True

    @commands.Cog.listener()
    async def on_ready(self):
        await self.ensure_table()

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.MissingRequiredArgument):
            await ctx.send("ℹ️ Missing required argument. Use `help journal` for usage.", ephemeral=True)
        elif isinstance(error, JournalError):
            await ctx.send(f"⚠️ Journal error: {error}", ephemeral=True)
        else:
            logger.error(f"Unhandled error: {error}")
            await ctx.send("❌ An unexpected error occurred.", ephemeral=True)

    @commands.group(invoke_without_command=True)
    async def journal(self, ctx):
        """Your private memory journal with rich visual interface."""
        prefix = ctx.clean_prefix
        embed = discord.Embed(
            title="Personal Journal System",
            description=(
                "A private space for your thoughts and memories\n\n"
                f"`{prefix}journal add` - Write a new journal entry\n"
                f"`{prefix}journal browse` - Browse your journal visually\n"
                f"`{prefix}journal random` - View a random entry\n"
                f"`{prefix}journal list` - List your entries\n"
                f"`{prefix}journal delete <id>` - Delete an entry\n"
                f"`{prefix}journal export` - Export your journal\n"
                f"`{prefix}journal stats` - View your journal statistics"
            ),
            color=0x9b59b6
        )
        await ctx.send(embed=embed)

    @journal.command(name="add")
    async def journal_add(self, ctx):
        """Add a new journal entry using a rich modal."""
        modal = JournalEntryModal(self)
        await ctx.send_modal(modal)

    @journal.command(name="browse")
    async def journal_browse(self, ctx, entry_id: int = None):
        """Browse your journal with visual interface."""
        try:
            entries = await self.bot.cxn.fetch(
                "SELECT id, entry, mood, created_at FROM journal_entries "
                "WHERE user_id = $1 ORDER BY created_at",
                ctx.author.id
            )
            
            if not entries:
                return await ctx.send("📭 Your journal is empty. Add your first entry with `journal add`", ephemeral=True)
            
            starting_index = 0
            if entry_id:
                for idx, entry in enumerate(entries):
                    if entry['id'] == entry_id:
                        starting_index = idx
                        break
                else:
                    return await ctx.send(f"🔍 Entry #{entry_id} not found", ephemeral=True)
            
            view = JournalBrowser(self, entries, starting_index, ctx.author.id)
            entry = entries[starting_index]
            
            img_buffer = await view.render_page(entry)
            if not img_buffer:
                return await ctx.send("❌ Failed to render journal page", ephemeral=True)
            
            file = discord.File(img_buffer, filename="journal.png")
            embed = discord.Embed(
                title=f"Journal Entry #{entry['id']}",
                description=f"Page {starting_index+1} of {len(entries)}",
                color=0x9b59b6
            )
            embed.set_image(url="attachment://journal.png")
            embed.set_footer(text=f"Created on {entry['created_at'].strftime('%b %d, %Y at %H:%M')}")
            
            message = await ctx.send(embed=embed, file=file, view=view)
            view.message = message
            
        except Exception as e:
            logger.error(f"Browse failed: {e}")
            await ctx.send("❌ Failed to load journal", ephemeral=True)

    @journal.command(name="random")
    async def journal_random(self, ctx):
        """View a random journal entry visually."""
        try:
            entries = await self.bot.cxn.fetch(
                "SELECT id, entry, mood, created_at FROM journal_entries "
                "WHERE user_id = $1 ORDER BY created_at",
                ctx.author.id
            )
            
            if not entries:
                return await ctx.send("📭 Your journal is empty. Add your first entry with `journal add`", ephemeral=True)
            
            starting_index = random.randint(0, len(entries) - 1)
            view = JournalBrowser(self, entries, starting_index, ctx.author.id)
            entry = entries[starting_index]
            
            img_buffer = await view.render_page(entry)
            if not img_buffer:
                return await ctx.send("❌ Failed to render journal page", ephemeral=True)
            
            file = discord.File(img_buffer, filename="journal.png")
            embed = discord.Embed(
                title="🎲 Random Journal Entry",
                description=f"Page {starting_index+1} of {len(entries)}",
                color=0x3498db
            )
            embed.set_image(url="attachment://journal.png")
            embed.set_footer(text=f"Created on {entry['created_at'].strftime('%b %d, %Y at %H:%M')}")
            
            message = await ctx.send(embed=embed, file=file, view=view)
            view.message = message
            
        except Exception as e:
            logger.error(f"Random view failed: {e}")
            await ctx.send("❌ Failed to load random entry", ephemeral=True)

    @journal.command(name="list")
    async def journal_list(self, ctx, page: int = 1):
        """List your journal entries."""
        if page < 1:
            return await ctx.send("ℹ️ Page number must be 1 or higher", ephemeral=True)

        try:
            total = await self.bot.cxn.fetchval(
                "SELECT COUNT(*) FROM journal_entries WHERE user_id = $1",
                ctx.author.id
            )
            
            if total == 0:
                return await ctx.send("📭 No journal entries found", ephemeral=True)

            per_page = 8
            offset = (page - 1) * per_page
            total_pages = (total + per_page - 1) // per_page

            if page > total_pages:
                return await ctx.send(f"ℹ️ Page {page} doesn't exist. Maximum page: {total_pages}", ephemeral=True)

            entries = await self.bot.cxn.fetch(
                """SELECT id, entry, created_at 
                FROM journal_entries 
                WHERE user_id = $1 
                ORDER BY created_at DESC 
                LIMIT $2 OFFSET $3""",
                ctx.author.id, per_page, offset
            )

            embed = discord.Embed(
                title=f"Journal Entries (Page {page}/{total_pages})",
                color=0x3498db
            )

            for entry in entries:
                preview = entry["entry"][:70] + "..." if len(entry["entry"]) > 70 else entry["entry"]
                date_str = entry["created_at"].strftime("%b %d, %Y")
                embed.add_field(
                    name=f"ID {entry['id']} • {date_str}",
                    value=preview,
                    inline=False
                )

            embed.set_footer(text=f"Total entries: {total} • Use 'journal browse <id>' to view entries")
            await ctx.send(embed=embed, ephemeral=True)
            
        except Exception as e:
            logger.error(f"List failed: {e}")
            await ctx.send("❌ Failed to list entries", ephemeral=True)

    @journal.command(name="delete")
    async def journal_delete(self, ctx, entry_id: int):
        """Delete a journal entry by ID."""
        try:
            entry = await self.bot.cxn.fetchrow(
                "SELECT id, entry FROM journal_entries WHERE user_id = $1 AND id = $2",
                ctx.author.id, entry_id
            )
            
            if not entry:
                return await ctx.send("🔍 Entry not found", ephemeral=True)

            preview = entry["entry"][:100] + "..." if len(entry["entry"]) > 100 else entry["entry"]
            embed = discord.Embed(
                title="Confirm Deletion",
                description=f"Delete entry #{entry_id}?\n\n**Preview:** {preview}",
                color=0xe74c3c
            )
            
            await ctx.send(embed=embed, view=DeleteButtonView(self, entry_id, ctx.author.id), ephemeral=True)
                
        except Exception as e:
            logger.error(f"Delete failed: {e}")
            await ctx.send("❌ Failed to delete entry", ephemeral=True)

    @journal.command(name="export")
    async def journal_export(self, ctx, format_type: str = "txt"):
        """Export your journal entries."""
        if format_type not in ["txt", "json"]:
            return await ctx.send("ℹ️ Supported formats: txt, json", ephemeral=True)

        try:
            entries = await self.bot.cxn.fetch(
                "SELECT id, entry, created_at FROM journal_entries WHERE user_id = $1 ORDER BY created_at",
                ctx.author.id
            )
            
            if not entries:
                return await ctx.send("📭 No entries to export", ephemeral=True)

            if format_type == "txt":
                content = self._export_as_txt(entries)
                filename = f"journal_export_{datetime.now().strftime('%Y%m%d')}.txt"
            else:
                content = self._export_as_json(entries)
                filename = f"journal_export_{datetime.now().strftime('%Y%m%d')}.json"

            file = discord.File(io.BytesIO(content.encode()), filename=filename)
            
            try:
                await ctx.author.send(f"📤 Your journal export ({len(entries)} entries):", file=file)
                await ctx.send("✅ Export sent to your DMs", ephemeral=True)
            except discord.Forbidden:
                await ctx.send("❌ Could not send DM. Please enable DMs", ephemeral=True)
                
        except Exception as e:
            logger.error(f"Export failed: {e}")
            await ctx.send("❌ Export failed", ephemeral=True)

    @journal.command(name="stats")
    async def journal_stats(self, ctx):
        """View your journal statistics."""
        try:
            stats = await self.bot.cxn.fetchrow(
                """SELECT 
                    COUNT(*) as total_entries,
                    MIN(created_at) as first_entry,
                    MAX(created_at) as latest_entry,
                    AVG(LENGTH(entry)) as avg_length
                FROM journal_entries 
                WHERE user_id = $1""",
                ctx.author.id
            )
            
            if stats["total_entries"] == 0:
                return await ctx.send("📭 No journal entries found", ephemeral=True)

            embed = discord.Embed(
                title="Journal Statistics",
                color=0x9b59b6
            )
            
            embed.add_field(name="Total Entries", value=str(stats["total_entries"]), inline=True)
            embed.add_field(name="Average Length", value=f"{int(stats['avg_length'])} characters", inline=True)
            embed.add_field(name="First Entry", value=stats["first_entry"].strftime("%b %d, %Y"), inline=True)
            embed.add_field(name="Latest Entry", value=stats["latest_entry"].strftime("%b %d, %Y"), inline=True)
            
            days_active = (stats["latest_entry"] - stats["first_entry"]).days + 1
            embed.add_field(name="Days Active", value=str(days_active), inline=True)
            
            await ctx.send(embed=embed, ephemeral=True)
            
        except Exception as e:
            logger.error(f"Stats failed: {e}")
            await ctx.send("❌ Failed to get statistics", ephemeral=True)

    def _export_as_txt(self, entries):
        lines = [f"Journal Export - {datetime.now().strftime('%Y-%m-%d %H:%M')}", "=" * 50, ""]
        for entry in entries:
            date = entry["created_at"].strftime("%Y-%m-%d %H:%M")
            lines.extend([
                f"Entry #{entry['id']} - {date}",
                "-" * 30,
                entry["entry"],
                ""
            ])
        return "\n".join(lines)

    def _export_as_json(self, entries):
        data = {
            "export_date": datetime.now().isoformat(),
            "total_entries": len(entries),
            "entries": [
                {
                    "id": entry["id"],
                    "content": entry["entry"],
                    "created_at": entry["created_at"].isoformat()
                }
                for entry in entries
            ]
        }
        return json.dumps(data, indent=2, ensure_ascii=False)

class DeleteButtonView(discord.ui.View):
    """View for delete confirmation."""
    def __init__(self, cog, entry_id, user_id):
        super().__init__(timeout=60)
        self.cog = cog
        self.entry_id = entry_id
        self.user_id = user_id

    async def interaction_check(self, interaction):
        return interaction.user.id == self.user_id

    @discord.ui.button(label="Confirm Delete", style=discord.ButtonStyle.danger)
    async def confirm(self, interaction, button):
        try:
            await self.cog.bot.cxn.execute(
                "DELETE FROM journal_entries WHERE id = $1 AND user_id = $2",
                self.entry_id, interaction.user.id
            )
            embed = discord.Embed(
                title="🗑️ Entry Deleted",
                description=f"Entry #{self.entry_id} has been permanently removed.",
                color=0x00ff00
            )
            await interaction.response.edit_message(embed=embed, view=None)
        except Exception as e:
            logger.error(f"Delete failed: {e}")
            await interaction.response.edit_message(content="❌ Failed to delete entry", view=None)

    @discord.ui.button(label="Cancel", style=discord.ButtonStyle.secondary)
    async def cancel(self, interaction, button):
        embed = discord.Embed(
            title="Deletion Cancelled",
            description="Entry was not deleted.",
            color=0x95a5a6
        )
        await interaction.response.edit_message(embed=embed, view=None)