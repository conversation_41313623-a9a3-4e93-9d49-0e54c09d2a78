import io
import time
import typing
import asyncio
import discord
import inspect

from datetime import datetime, timedelta
from discord.ext import commands, menus
from PIL import Image, ImageDraw, ImageFont

from utilities import utils
from utilities import checks
from utilities import images
from utilities import cleaner
from utilities import humantime
from utilities import converters
from utilities import decorators
from utilities import formatting
from utilities import pagination


async def setup(bot):
    await bot.add_cog(Leaderboard(bot))


class Leaderboard(commands.Cog):
    """
    Module for message leaderboards with auto-refresh
    """

    def __init__(self, bot):
        self.bot = bot
        self.active_leaderboards = {}  # Store active leaderboard messages by message_id

    @decorators.command(
        brief="Show auto-refreshing message leaderboard.",
        invoke_without_command=True,
        case_insensitive=True,
        implemented="2021-03-13 04:47:25.624232",
        updated="2021-05-07 04:26:00.620200",
        examples="""
                {0}leaderboard
                {0}leaderboard daily
                {0}leaderboard weekly
                {0}leaderboard monthly
                {0}leaderboard yearly
                """,
    )
    @checks.guild_only()
    @checks.bot_has_perms(embed_links=True, manage_messages=True)
    @checks.cooldown()
    async def leaderboard(self, ctx, unit: str = "monthly", channel: typing.Optional[discord.TextChannel] = None):
        """
        Usage: {0}leaderboard [unit] [channel]
        Permission: Manage Messages
        Output:
            Shows an auto-refreshing message leaderboard
            that updates every 5 minutes. Users can choose
            between daily, weekly, monthly, or yearly stats.
        Notes:
            The leaderboard will automatically refresh every 5 minutes.
            Only one active leaderboard per channel is allowed.
            Use 'stop' to stop the auto-refresh.
        """
        unit = unit.lower()
        valid_units = ["daily", "weekly", "monthly", "yearly"]
        
        if unit not in valid_units:
            unit = "monthly"
        
        target_channel = channel or ctx.channel
        
        # Create initial leaderboard data
        leaderboard_data = await self._get_leaderboard_data(ctx, unit, target_channel)
        if not leaderboard_data:
            return
        
        # Create pagination menu
        menu = LeaderboardMenu(leaderboard_data, unit, target_channel, self)
        
        # Send the initial message
        message = await menu.start(ctx)
        
        if message:
            # Store the leaderboard info using message ID as key
            self.active_leaderboards[message.id] = {
                'message': message,
                'unit': unit,
                'channel': target_channel,
                'guild': ctx.guild,
                'menu': menu,
                'task': None
            }
            
            # Start the auto-refresh task
            task = asyncio.create_task(self._auto_refresh_leaderboard(message.id))
            self.active_leaderboards[message.id]['task'] = task

    @decorators.command(
        name="leaderboardstop",
        brief="Stop auto-refreshing leaderboard(s) by message ID or channel.",
        examples="{0}leaderboardstop [message_id] [channel]",
    )
    @checks.guild_only()
    @checks.bot_has_perms(embed_links=True, manage_messages=True)
    async def leaderboardstop(
        self,
        ctx,
        message_id: typing.Optional[int] = None,
        channel: typing.Optional[discord.TextChannel] = None,
    ):
        """
        Usage: {0}leaderboardstop [message_id] [channel]
        Stops a specific leaderboard by message ID, or all in a channel if no ID is given.
        """
        target_channel = channel or ctx.channel

        if message_id:
            info = self.active_leaderboards.get(message_id)
            if info and info['channel'].id == target_channel.id:
                if info['task']:
                    info['task'].cancel()
                del self.active_leaderboards[message_id]
                await ctx.send_or_reply(f"Stopped leaderboard for message ID `{message_id}` in {target_channel.mention}.")
            else:
                await ctx.send_or_reply(f"No active leaderboard found for message ID `{message_id}` in {target_channel.mention}.")
            return

        # If no message_id, stop all in channel
        stopped = 0
        to_remove = []
        for msg_id, info in list(self.active_leaderboards.items()):
            if info['channel'].id == target_channel.id:
                if info['task']:
                    info['task'].cancel()
                to_remove.append(msg_id)
                stopped += 1

        for msg_id in to_remove:
            del self.active_leaderboards[msg_id]

        if stopped:
            await ctx.send_or_reply(f"Stopped {stopped} leaderboard{'s' if stopped > 1 else ''} in {target_channel.mention}.")
        else:
            await ctx.send_or_reply("No active leaderboard found in this channel.")

    async def _get_leaderboard_data(self, ctx, unit: str, target_channel: discord.TextChannel = None):
        """Get the leaderboard data from database."""
        # Time calculations
        time_dict = {
            "daily": 86400,
            "weekly": 604800,
            "monthly": 2592000,
            "yearly": 31556952
        }
        
        time_seconds = time_dict[unit]
        now = int(time.time())
        diff = now - time_seconds
        
        # Query setup
        if target_channel and target_channel != ctx.channel:
            condition = "AND channel_id = $3"
            args = (ctx.guild.id, diff, target_channel.id)
            title_suffix = f" in {target_channel.mention}"
        else:
            condition = ""
            args = (ctx.guild.id, diff)
            title_suffix = ""
        
        query = f"""
                SELECT COUNT(*) as c, author_id
                FROM messages
                WHERE server_id = $1
                AND unix > $2
                {condition}
                GROUP BY author_id
                ORDER BY c DESC;
                """
        
        try:
            records = await self.bot.cxn.fetch(query, *args)
        except Exception as e:
            await ctx.send_or_reply(f"Database error: {str(e)}")
            return None
        
        if not records:
            await ctx.send_or_reply(f"No message data available for the last {unit.rstrip('ly')}.")
            return None
        
        # Process records to get user data
        user_data = []
        for record in records:
            # record is a dict, not an object
            member = ctx.guild.get_member(record['author_id'])
            if member:
                name = member.display_name
            else:
                name = f"Unknown User ({record['author_id']})"
            
            user_data.append({
                'name': name,
                'count': record['c'],
                'author_id': record['author_id']
            })
        
        return {
            'users': user_data,
            'total_messages': sum(record['c'] for record in records),
            'total_users': len(records),
            'unit': unit,
            'title_suffix': title_suffix
        }

    async def _auto_refresh_leaderboard(self, message_id: int):
        """Auto-refresh the leaderboard every 5 minutes."""
        while message_id in self.active_leaderboards:
            try:
                await asyncio.sleep(300)  # 5 minutes
                
                if message_id not in self.active_leaderboards:
                    break
                
                leaderboard_info = self.active_leaderboards[message_id]
                unit = leaderboard_info['unit']
                channel = leaderboard_info['channel']
                guild = leaderboard_info['guild']
                menu = leaderboard_info['menu']
                
                # Create a mock context for the data retrieval
                class MockContext:
                    def __init__(self, guild, channel, bot):
                        self.guild = guild
                        self.channel = channel
                        self.bot = bot
                    
                    async def send_or_reply(self, content):
                        pass  # We don't need this for data retrieval
                
                mock_ctx = MockContext(guild, channel, self.bot)
                
                # Get updated data
                leaderboard_data = await self._get_leaderboard_data(mock_ctx, unit, channel)
                
                if leaderboard_data and menu:
                    try:
                        await menu.update_data(leaderboard_data)
                    except discord.NotFound:
                        # Message was deleted, stop the refresh
                        del self.active_leaderboards[message_id]
                        break
                    except discord.Forbidden:
                        # No permission to edit, stop the refresh
                        del self.active_leaderboards[message_id]
                        break
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in leaderboard auto-refresh: {e}")
                continue

    def cog_unload(self):
        """Clean up when the cog is unloaded."""
        for leaderboard_info in self.active_leaderboards.values():
            if leaderboard_info['task']:
                leaderboard_info['task'].cancel()
            if leaderboard_info['menu']:
                leaderboard_info['menu'].stop()
        self.active_leaderboards.clear()


class LeaderboardMenu(menus.MenuPages):
    """Paginated menu for leaderboard display."""
    
    def __init__(self, data, unit, channel, cog):
        self.data = data
        self.unit = unit
        self.channel = channel
        self.cog = cog
        source = LeaderboardPageSource(data, unit, channel)
        super().__init__(source, timeout=None, delete_message_after=False)
    
    async def start(self, ctx):
        """Start the menu and return the message."""
        await super().start(ctx, wait=False)
        return self.message
    
    async def update_data(self, new_data):
        """Update the leaderboard data and refresh the current page."""
        self.data = new_data
        self.source.data = new_data

        # Refresh current page
        if self.message:
            page = await self.source.get_page(self.current_page)
            await self.message.edit(embed=page)


class LeaderboardPageSource(menus.ListPageSource):
    """Page source for leaderboard pagination."""
    
    def __init__(self, data, unit, channel):
        self.data = data
        self.unit = unit
        self.channel = channel
        per_page = 20  # Show 20 users per page
        super().__init__(data['users'], per_page=per_page)
    
    async def format_page(self, menu, entries):
        """Format a page of the leaderboard."""
        offset = menu.current_page * self.per_page
        
        embed = discord.Embed(
            title=f"Message Leaderboard - {self.unit.title()}{self.data['title_suffix']}",
            description=f"{self.data['total_messages']} messages from {self.data['total_users']} user{'s' if self.data['total_users'] != 1 else ''} in the last {self.unit.rstrip('ly')}",
            color=menu.cog.bot.mode.EMBED_COLOR,
            timestamp=discord.utils.utcnow()
        )
        
        # Add users to embed
        for i, user in enumerate(entries, start=offset + 1):
            embed.add_field(
                name=f"{i}. {user['name']}",
                value=f"{user['count']} message{'s' if user['count'] != 1 else ''}",
                inline=True
            )
        
        # Add page info to footer
        max_pages = self.get_max_pages()
        if max_pages > 1:
            embed.set_footer(text=f"Page {menu.current_page + 1}/{max_pages} | Auto-refreshes every 5 minutes")
        else:
            embed.set_footer(text="Auto-refreshes every 5 minutes")
        
        return embed